/**
 * 豆包实时语音大模型API客户端
 */

import { 
  createClientEventMessage, 
  createAudioMessage,
  ClientEventId,
  ServerEventId,
  decodeHeader,
  MessageType
} from './protocol';

// 连接配置
export interface DoubaoConfig {
  resourceId?: string;
  connectId?: string;
}

// 后端连接信息
interface ConnectionInfo {
  wsUrl: string;
  headers: {
    'X-Api-App-ID': string;
    'X-Api-Access-Key': string;
    'X-Api-Resource-Id': string;
  };
}

// 会话配置
export interface SessionConfig {
  botName?: string;
  dialogId?: string;
  strictAudit?: boolean;
  ttsConfig?: {
    audioConfig?: {
      channel: number;
      format: 'pcm' | 'ogg';
      sampleRate: number;
    };
  };
}

// 事件类型定义
export interface ServerEvent {
  eventId: ServerEventId;
  sessionId?: string;
  data?: any;
  audioData?: ArrayBuffer;
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 会话状态
export enum SessionState {
  IDLE = 'idle',
  STARTING = 'starting',
  ACTIVE = 'active',
  ENDING = 'ending',
  ERROR = 'error'
}

/**
 * 豆包实时语音客户端
 */
export class DoubaoRealtimeClient {
  private ws: WebSocket | null = null;
  private config: DoubaoConfig;
  private connectionInfo: ConnectionInfo | null = null;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private sessionState: SessionState = SessionState.IDLE;
  private currentSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  // 实时对话状态管理
  private isProcessingAudio = false;
  private lastAudioTime = 0;
  private audioProcessingTimeout: NodeJS.Timeout | null = null;
  private currentConversationId: string | null = null;

  constructor(config: DoubaoConfig = {}) {
    this.config = {
      resourceId: 'volc.speech.dialog',
      ...config
    };
  }

  /**
   * 连接到豆包API
   * 从后端获取连接信息，确保API密钥安全
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.connectionState = ConnectionState.CONNECTING;
    this.emit('connectionStateChange', this.connectionState);

    try {
      // 从后端获取连接信息
      const response = await fetch('/api/realtime', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getConnectionInfo'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get connection info from backend');
      }

      this.connectionInfo = await response.json();

      // 在演示模式下，我们模拟连接成功
      // 实际应用中这里会建立真实的WebSocket连接
      console.log('从后端获取连接信息成功，模拟连接到豆包API');

      // 模拟连接延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      this.connectionState = ConnectionState.CONNECTED;
      this.emit('connectionStateChange', this.connectionState);

      // 模拟发送StartConnection事件
      console.log('演示模式：发送StartConnection事件');

      return;

      // 在实际应用中，这里应该是真实的WebSocket连接代码
      // const url = this.connectionInfo.wsUrl;
      // this.ws = new WebSocket(url);
      // this.ws.binaryType = 'arraybuffer';
      // ... 设置事件处理器和认证头

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.currentSessionId) {
      this.endSession();
    }
    
    this.sendFinishConnection();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 开始会话（演示模式）
   */
  async startSession(config: SessionConfig = {}): Promise<string> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('未连接到服务器');
    }

    if (this.sessionState !== SessionState.IDLE) {
      throw new Error('会话已存在');
    }

    this.sessionState = SessionState.STARTING;
    this.currentSessionId = this.generateSessionId();

    console.log('演示模式：启动会话', {
      sessionId: this.currentSessionId,
      config
    });

    // 模拟会话启动延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    this.sessionState = SessionState.ACTIVE;

    // 模拟会话启动成功事件
    setTimeout(() => {
      this.emit('serverEvent', {
        eventId: ServerEventId.SESSION_STARTED,
        sessionId: this.currentSessionId,
        data: { dialog_id: 'demo-dialog-id' }
      });
    }, 100);

    return this.currentSessionId;
  }

  /**
   * 结束会话
   */
  endSession(): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      return;
    }

    this.sessionState = SessionState.ENDING;

    const message = createClientEventMessage(
      ClientEventId.FINISH_SESSION,
      this.currentSessionId,
      {}
    );

    this.sendBinary(message);
    
    this.currentSessionId = null;
    this.sessionState = SessionState.IDLE;
  }

  /**
   * 发送音频数据（演示模式 - 实时对话优化）
   */
  sendAudio(audioData: ArrayBuffer): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    const now = Date.now();
    this.lastAudioTime = now;

    // 如果正在处理音频，只更新时间戳，不触发新的处理流程
    if (this.isProcessingAudio) {
      console.log('演示模式：音频处理中，跳过重复处理');
      return;
    }

    console.log('演示模式：开始处理音频数据', audioData.byteLength, '字节');
    this.isProcessingAudio = true;

    // 生成新的对话ID
    this.currentConversationId = `conv-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;

    // 清除之前的超时
    if (this.audioProcessingTimeout) {
      clearTimeout(this.audioProcessingTimeout);
    }

    // 模拟ASR开始识别
    setTimeout(() => {
      if (this.currentConversationId) {
        this.emit('serverEvent', {
          eventId: ServerEventId.ASR_INFO,
          sessionId: this.currentSessionId,
          conversationId: this.currentConversationId,
          data: { firstWord: true }
        });
      }
    }, 200);

    // 设置音频处理超时，模拟说话结束后的处理
    this.audioProcessingTimeout = setTimeout(() => {
      this.processAudioComplete();
    }, 2000); // 2秒后认为说话结束
  }

  /**
   * 处理音频完成后的逻辑
   */
  private processAudioComplete(): void {
    if (!this.isProcessingAudio || !this.currentConversationId) {
      return;
    }

    console.log('演示模式：音频处理完成，生成回复');

    const conversationId = this.currentConversationId;

    // 模拟ASR识别结果
    setTimeout(() => {
      if (conversationId === this.currentConversationId) {
        const responses = [
          '你好',
          '今天天气怎么样',
          '请介绍一下你自己',
          '能帮我做什么',
          '谢谢你的帮助'
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];

        this.emit('serverEvent', {
          eventId: ServerEventId.ASR_RESPONSE,
          sessionId: this.currentSessionId,
          conversationId,
          data: {
            results: [{
              text: randomResponse,
              is_interim: false
            }]
          }
        });
      }
    }, 300);

    // 模拟聊天响应
    setTimeout(() => {
      if (conversationId === this.currentConversationId) {
        const chatResponses = [
          '你好！我是豆包AI助手，很高兴为您服务！',
          '今天天气很不错呢，阳光明媚，适合出门走走。',
          '我是豆包，一个智能AI助手，可以帮您解答问题、聊天对话。',
          '我可以帮您回答问题、提供信息、进行对话交流等。',
          '不客气！随时为您服务，有什么需要尽管说。'
        ];
        const randomChat = chatResponses[Math.floor(Math.random() * chatResponses.length)];

        this.emit('serverEvent', {
          eventId: ServerEventId.CHAT_RESPONSE,
          sessionId: this.currentSessionId,
          conversationId,
          data: {
            content: randomChat
          }
        });
      }
    }, 600);

    // 模拟TTS开始事件
    setTimeout(() => {
      if (conversationId === this.currentConversationId) {
        this.emit('serverEvent', {
          eventId: ServerEventId.TTS_SENTENCE_START,
          sessionId: this.currentSessionId,
          conversationId,
          data: {
            tts_type: 'default',
            text: '开始语音合成'
          }
        });
      }
    }, 800);

    // 模拟TTS音频响应
    setTimeout(() => {
      if (conversationId === this.currentConversationId) {
        // 根据文档，可以返回PCM或OGG格式
        // 这里我们创建PCM格式的演示数据
        const demoPCMAudioData = this.createDemoPCMAudio();
        this.emit('serverEvent', {
          eventId: ServerEventId.TTS_RESPONSE,
          sessionId: this.currentSessionId,
          conversationId,
          audioData: demoPCMAudioData,
          audioFormat: 'pcm' // 标记音频格式
        });
      }
    }, 1000);

    // 模拟TTS结束事件
    setTimeout(() => {
      if (conversationId === this.currentConversationId) {
        this.emit('serverEvent', {
          eventId: ServerEventId.TTS_ENDED,
          sessionId: this.currentSessionId,
          conversationId,
          data: {}
        });

        // 重置处理状态
        this.isProcessingAudio = false;
        this.currentConversationId = null;
      }
    }, 1500);
  }

  /**
   * 创建演示用的PCM音频数据（根据文档要求）
   */
  private createDemoPCMAudio(): ArrayBuffer {
    // 根据文档：服务端返回PCM格式（单声道、24000Hz采样率、Float32采样点、小端序）
    const sampleRate = 24000;
    const duration = 2.0; // 2秒
    const samples = Math.floor(sampleRate * duration);

    // 创建Float32Array用于PCM数据
    const pcmData = new Float32Array(samples);

    // 生成一个更复杂的音频信号，模拟语音
    const baseFreq = 200; // 基频200Hz，模拟人声
    const volume = 0.3; // 音量

    for (let i = 0; i < samples; i++) {
      const t = i / sampleRate;

      // 基础正弦波
      let signal = Math.sin(2 * Math.PI * baseFreq * t);

      // 添加谐波，模拟语音特征
      signal += 0.5 * Math.sin(2 * Math.PI * baseFreq * 2 * t); // 二次谐波
      signal += 0.3 * Math.sin(2 * Math.PI * baseFreq * 3 * t); // 三次谐波

      // 添加包络，模拟语音的起伏
      const envelope = Math.sin(Math.PI * t / duration); // 整体包络
      const modulation = 1 + 0.3 * Math.sin(2 * Math.PI * 5 * t); // 5Hz调制

      pcmData[i] = volume * signal * envelope * modulation;

      // 确保在有效范围内
      if (pcmData[i] > 1.0) pcmData[i] = 1.0;
      if (pcmData[i] < -1.0) pcmData[i] = -1.0;
    }

    console.log(`生成演示PCM音频: ${samples}样本, ${sampleRate}Hz, ${duration}秒`);
    return pcmData.buffer;
  }

  /**
   * 创建演示用的OGG/Opus音频数据
   */
  private createDemoOggAudio(): ArrayBuffer {
    // 创建一个最小的有效OGG/Opus文件头
    // 这是一个简化的OGG页面结构
    const oggPage = new Uint8Array([
      // OGG页面头部
      0x4F, 0x67, 0x67, 0x53, // "OggS" 魔数
      0x00,                   // 版本
      0x02,                   // 页面类型（开始页面）
      0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 颗粒位置
      0x00, 0x00, 0x00, 0x01, // 流序列号
      0x00, 0x00, 0x00, 0x00, // 页面序列号
      0x00, 0x00, 0x00, 0x00, // CRC校验和（简化为0）
      0x01,                   // 页面段数
      0x13,                   // 段长度

      // Opus头部包
      0x4F, 0x70, 0x75, 0x73, 0x48, 0x65, 0x61, 0x64, // "OpusHead"
      0x01,                   // 版本
      0x01,                   // 声道数
      0x00, 0x00,            // 预跳过
      0x80, 0x3E, 0x00, 0x00, // 输入采样率（16000Hz）
      0x00, 0x00,            // 输出增益
      0x00                    // 声道映射族
    ]);

    return oggPage.buffer;
  }

  /**
   * 发送打招呼文本（演示模式）
   */
  sayHello(content: string): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('演示模式：发送打招呼', content);

    // 模拟聊天响应
    setTimeout(() => {
      this.emit('serverEvent', {
        eventId: ServerEventId.CHAT_RESPONSE,
        sessionId: this.currentSessionId,
        data: {
          content: `你好！我收到了你的问候："${content}"。我是豆包AI助手，很高兴认识你！`
        }
      });
    }, 1000);

    // 模拟TTS音频响应
    setTimeout(() => {
      const demoAudioData = new ArrayBuffer(2048);
      this.emit('serverEvent', {
        eventId: ServerEventId.TTS_RESPONSE,
        sessionId: this.currentSessionId,
        audioData: demoAudioData
      });
    }, 1500);
  }

  /**
   * 发送TTS文本
   */
  sendTTSText(content: string, start: boolean = false, end: boolean = false): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    const message = createClientEventMessage(
      ClientEventId.CHAT_TTS_TEXT,
      this.currentSessionId,
      { start, content, end }
    );

    this.sendBinary(message);
  }

  /**
   * 事件监听
   */
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听
   */
  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取连接状态
   */
  get state(): ConnectionState {
    return this.connectionState;
  }

  /**
   * 获取会话状态
   */
  get session(): SessionState {
    return this.sessionState;
  }

  // 私有方法

  private sendStartConnection(): void {
    const connectId = this.config.connectId || this.generateConnectId();
    const message = createClientEventMessage(
      ClientEventId.START_CONNECTION,
      '',
      {},
      connectId
    );
    this.sendBinary(message);
  }

  private sendFinishConnection(): void {
    if (this.currentSessionId) {
      const message = createClientEventMessage(
        ClientEventId.FINISH_CONNECTION,
        this.currentSessionId,
        {}
      );
      this.sendBinary(message);
    }
  }

  private sendBinary(data: Buffer): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(data);
    }
  }

  private handleMessage(data: ArrayBuffer): void {
    try {
      const buffer = Buffer.from(data);
      const header = decodeHeader(buffer);
      
      // 解析消息内容（简化版本，实际需要完整解析可选字段）
      const event: ServerEvent = {
        eventId: 0, // 需要从可选字段中解析
        data: null,
        audioData: header.messageType === MessageType.AUDIO_ONLY_RESPONSE ? data : undefined
      };

      this.emit('serverEvent', event);
      
    } catch (error) {
      console.error('Failed to handle message:', error);
      this.emit('error', error);
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConnectId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
