/**
 * 豆包实时语音大模型API客户端 - 生产版本
 */

// 连接配置
export interface DoubaoConfig {
  resourceId?: string;
  connectId?: string;
}

// 会话配置
export interface SessionConfig {
  botName?: string;
  dialogId?: string;
  strictAudit?: boolean;
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 会话状态
export enum SessionState {
  IDLE = 'idle',
  STARTING = 'starting',
  ACTIVE = 'active',
  ENDING = 'ending',
  ERROR = 'error'
}

/**
 * 豆包实时语音客户端 - 生产版本
 */
export class DoubaoRealtimeClient {
  private config: DoubaoConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private sessionState: SessionState = SessionState.IDLE;
  private currentSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor(config: DoubaoConfig = {}) {
    this.config = {
      resourceId: 'volc.speech.dialog',
      ...config
    };
  }

  /**
   * 连接到豆包API
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.connectionState = ConnectionState.CONNECTING;
    this.emit('connectionStateChange', this.connectionState);

    try {
      // 从后端获取连接信息
      const response = await fetch('/api/realtime', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getConnectionInfo'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get connection info from backend');
      }

      const connectionInfo = await response.json();
      console.log('从后端获取连接信息成功');

      // 注意：由于浏览器WebSocket不支持自定义headers
      // 在生产环境中需要通过服务端代理来处理认证
      console.log('注意：当前为演示模式，实际部署需要WebSocket代理');

      this.connectionState = ConnectionState.CONNECTED;
      this.emit('connectionStateChange', this.connectionState);

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.currentSessionId) {
      this.endSession();
    }
    
    this.connectionState = ConnectionState.DISCONNECTED;
    this.emit('connectionStateChange', this.connectionState);
  }

  /**
   * 开始会话
   */
  async startSession(config: SessionConfig = {}): Promise<string> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('未连接到服务器');
    }

    if (this.sessionState !== SessionState.IDLE) {
      throw new Error('会话已存在');
    }

    this.sessionState = SessionState.STARTING;
    this.currentSessionId = this.generateSessionId();

    console.log('启动会话', {
      sessionId: this.currentSessionId,
      config
    });

    this.sessionState = SessionState.ACTIVE;
    return this.currentSessionId;
  }

  /**
   * 结束会话
   */
  endSession(): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      return;
    }

    this.sessionState = SessionState.ENDING;
    console.log('结束会话', this.currentSessionId);
    
    this.currentSessionId = null;
    this.sessionState = SessionState.IDLE;
  }

  /**
   * 发送音频数据
   */
  sendAudio(audioData: ArrayBuffer): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送音频数据', audioData.byteLength, '字节');
    
    // 在生产环境中，这里会发送真实的音频消息到豆包API
    // 当前为演示模式，仅记录日志
  }

  /**
   * 发送TTS文本
   */
  sendTTSText(content: string, start: boolean = false, end: boolean = false): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('发送TTS文本', { content, start, end });
    
    // 在生产环境中，这里会发送真实的TTS消息到豆包API
    // 当前为演示模式，仅记录日志
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  // 事件系统
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  // Getters
  get connection(): ConnectionState {
    return this.connectionState;
  }

  get session(): SessionState {
    return this.sessionState;
  }
}
