/**
 * 豆包实时语音大模型API客户端
 */

import { 
  createClientEventMessage, 
  createAudioMessage,
  ClientEventId,
  ServerEventId,
  decodeHeader,
  MessageType
} from './protocol';

// 连接配置
export interface DoubaoConfig {
  appId: string;
  accessKey: string;
  resourceId?: string;
  appKey?: string;
  connectId?: string;
}

// 会话配置
export interface SessionConfig {
  botName?: string;
  dialogId?: string;
  strictAudit?: boolean;
  ttsConfig?: {
    audioConfig?: {
      channel: number;
      format: 'pcm' | 'ogg';
      sampleRate: number;
    };
  };
}

// 事件类型定义
export interface ServerEvent {
  eventId: ServerEventId;
  sessionId?: string;
  data?: any;
  audioData?: ArrayBuffer;
}

// 连接状态
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 会话状态
export enum SessionState {
  IDLE = 'idle',
  STARTING = 'starting',
  ACTIVE = 'active',
  ENDING = 'ending',
  ERROR = 'error'
}

/**
 * 豆包实时语音客户端
 */
export class DoubaoRealtimeClient {
  private ws: WebSocket | null = null;
  private config: DoubaoConfig;
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED;
  private sessionState: SessionState = SessionState.IDLE;
  private currentSessionId: string | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor(config: DoubaoConfig) {
    this.config = {
      resourceId: 'volc.speech.dialog',
      appKey: 'PlgvMymc7f3tQnJ6',
      ...config
    };
  }

  /**
   * 连接到豆包API
   * 注意：这是一个演示版本，实际生产环境中需要通过服务端代理来处理认证
   */
  async connect(): Promise<void> {
    if (this.connectionState === ConnectionState.CONNECTED) {
      return;
    }

    this.connectionState = ConnectionState.CONNECTING;

    try {
      // 在演示模式下，我们模拟连接成功
      // 实际应用中需要通过服务端WebSocket代理来连接豆包API
      console.log('演示模式：模拟连接到豆包API');

      // 模拟连接延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      this.connectionState = ConnectionState.CONNECTED;
      this.emit('connectionStateChange', this.connectionState);

      // 模拟发送StartConnection事件
      console.log('演示模式：发送StartConnection事件');

      return;

      // 在实际应用中，这里应该是真实的WebSocket连接代码
      // const url = 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue';
      // this.ws = new WebSocket(url);
      // this.ws.binaryType = 'arraybuffer';
      // ... 设置事件处理器

    } catch (error) {
      this.connectionState = ConnectionState.ERROR;
      this.emit('connectionStateChange', this.connectionState);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.currentSessionId) {
      this.endSession();
    }
    
    this.sendFinishConnection();
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 开始会话（演示模式）
   */
  async startSession(config: SessionConfig = {}): Promise<string> {
    if (this.connectionState !== ConnectionState.CONNECTED) {
      throw new Error('未连接到服务器');
    }

    if (this.sessionState !== SessionState.IDLE) {
      throw new Error('会话已存在');
    }

    this.sessionState = SessionState.STARTING;
    this.currentSessionId = this.generateSessionId();

    console.log('演示模式：启动会话', {
      sessionId: this.currentSessionId,
      config
    });

    // 模拟会话启动延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    this.sessionState = SessionState.ACTIVE;

    // 模拟会话启动成功事件
    setTimeout(() => {
      this.emit('serverEvent', {
        eventId: ServerEventId.SESSION_STARTED,
        sessionId: this.currentSessionId,
        data: { dialog_id: 'demo-dialog-id' }
      });
    }, 100);

    return this.currentSessionId;
  }

  /**
   * 结束会话
   */
  endSession(): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      return;
    }

    this.sessionState = SessionState.ENDING;

    const message = createClientEventMessage(
      ClientEventId.FINISH_SESSION,
      this.currentSessionId,
      {}
    );

    this.sendBinary(message);
    
    this.currentSessionId = null;
    this.sessionState = SessionState.IDLE;
  }

  /**
   * 发送音频数据（演示模式）
   */
  sendAudio(audioData: ArrayBuffer): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('演示模式：发送音频数据', audioData.byteLength, '字节');

    // 模拟ASR响应
    setTimeout(() => {
      this.emit('serverEvent', {
        eventId: ServerEventId.ASR_RESPONSE,
        sessionId: this.currentSessionId,
        data: {
          results: [{
            text: '这是模拟的语音识别结果',
            is_interim: false
          }]
        }
      });
    }, 1000);

    // 模拟聊天响应
    setTimeout(() => {
      this.emit('serverEvent', {
        eventId: ServerEventId.CHAT_RESPONSE,
        sessionId: this.currentSessionId,
        data: {
          content: '你好！我是豆包，很高兴为您服务。这是演示模式的回复。'
        }
      });
    }, 2000);

    // 模拟TTS音频响应
    setTimeout(() => {
      // 创建一个简单的音频数据（静音）
      const demoAudioData = new ArrayBuffer(1024);
      this.emit('serverEvent', {
        eventId: ServerEventId.TTS_RESPONSE,
        sessionId: this.currentSessionId,
        audioData: demoAudioData
      });
    }, 2500);
  }

  /**
   * 发送打招呼文本（演示模式）
   */
  sayHello(content: string): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    console.log('演示模式：发送打招呼', content);

    // 模拟聊天响应
    setTimeout(() => {
      this.emit('serverEvent', {
        eventId: ServerEventId.CHAT_RESPONSE,
        sessionId: this.currentSessionId,
        data: {
          content: `你好！我收到了你的问候："${content}"。我是豆包AI助手，很高兴认识你！`
        }
      });
    }, 1000);

    // 模拟TTS音频响应
    setTimeout(() => {
      const demoAudioData = new ArrayBuffer(2048);
      this.emit('serverEvent', {
        eventId: ServerEventId.TTS_RESPONSE,
        sessionId: this.currentSessionId,
        audioData: demoAudioData
      });
    }, 1500);
  }

  /**
   * 发送TTS文本
   */
  sendTTSText(content: string, start: boolean = false, end: boolean = false): void {
    if (!this.currentSessionId || this.sessionState !== SessionState.ACTIVE) {
      throw new Error('会话未激活');
    }

    const message = createClientEventMessage(
      ClientEventId.CHAT_TTS_TEXT,
      this.currentSessionId,
      { start, content, end }
    );

    this.sendBinary(message);
  }

  /**
   * 事件监听
   */
  on(event: string, listener: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听
   */
  off(event: string, listener: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 获取连接状态
   */
  get state(): ConnectionState {
    return this.connectionState;
  }

  /**
   * 获取会话状态
   */
  get session(): SessionState {
    return this.sessionState;
  }

  // 私有方法

  private sendStartConnection(): void {
    const connectId = this.config.connectId || this.generateConnectId();
    const message = createClientEventMessage(
      ClientEventId.START_CONNECTION,
      '',
      {},
      connectId
    );
    this.sendBinary(message);
  }

  private sendFinishConnection(): void {
    if (this.currentSessionId) {
      const message = createClientEventMessage(
        ClientEventId.FINISH_CONNECTION,
        this.currentSessionId,
        {}
      );
      this.sendBinary(message);
    }
  }

  private sendBinary(data: Buffer): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(data);
    }
  }

  private handleMessage(data: ArrayBuffer): void {
    try {
      const buffer = Buffer.from(data);
      const header = decodeHeader(buffer);
      
      // 解析消息内容（简化版本，实际需要完整解析可选字段）
      const event: ServerEvent = {
        eventId: 0, // 需要从可选字段中解析
        data: null,
        audioData: header.messageType === MessageType.AUDIO_ONLY_RESPONSE ? data : undefined
      };

      this.emit('serverEvent', event);
      
    } catch (error) {
      console.error('Failed to handle message:', error);
      this.emit('error', error);
    }
  }

  private emit(event: string, ...args: any[]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => listener(...args));
    }
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateConnectId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
