'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Mic, MicOff, Volume2, VolumeX, Settings, Play, Square } from 'lucide-react';
import { AudioRecorder, AudioPlayer, detectAudioFormat } from '@/lib/audio-utils';
import { DoubaoRealtimeClient, DoubaoConfig, ConnectionState as DoubaoConnectionState, SessionState as DoubaoSessionState } from '@/lib/doubao-client';

// 连接状态类型
type ConnectionState = 'disconnected' | 'connecting' | 'connected' | 'error';
type SessionState = 'idle' | 'starting' | 'active' | 'ending' | 'error';

// 消息类型
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  audioData?: ArrayBuffer;
}

// 配置接口
interface VoiceChatConfig {
  appId: string;
  accessKey: string;
  botName?: string;
  strictAudit?: boolean;
}

/**
 * 语音聊天组件
 */
export function VoiceChat() {
  // 状态管理
  const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected');
  const [sessionState, setSessionState] = useState<SessionState>('idle');
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [config, setConfig] = useState<VoiceChatConfig>({
    appId: '',
    accessKey: '',
    botName: '豆包',
    strictAudit: true
  });
  const [showConfig, setShowConfig] = useState(true);
  const [volume, setVolume] = useState(0);

  // Refs
  const doubaoClientRef = useRef<DoubaoRealtimeClient | null>(null);
  const audioRecorderRef = useRef<AudioRecorder | null>(null);
  const audioPlayerRef = useRef<AudioPlayer | null>(null);
  const sessionIdRef = useRef<string | null>(null);

  // 初始化音频组件
  useEffect(() => {
    const initAudio = async () => {
      try {
        audioRecorderRef.current = new AudioRecorder();
        await audioRecorderRef.current.initialize();

        audioPlayerRef.current = new AudioPlayer();
        await audioPlayerRef.current.initialize();
      } catch (error) {
        console.error('Failed to initialize audio:', error);
        addMessage('system', '音频初始化失败，请检查麦克风权限');
      }
    };

    initAudio();

    return () => {
      audioRecorderRef.current?.cleanup();
      audioPlayerRef.current?.cleanup();
    };
  }, []);

  // 连接到豆包API
  const connect = useCallback(async () => {
    if (!config.appId || !config.accessKey) {
      addMessage('system', '请先配置App ID和Access Key');
      return;
    }

    try {
      setConnectionState('connecting');

      // 创建豆包客户端配置
      const doubaoConfig: DoubaoConfig = {
        appId: config.appId,
        accessKey: config.accessKey,
        resourceId: 'volc.speech.dialog',
        appKey: 'PlgvMymc7f3tQnJ6'
      };

      // 创建豆包客户端
      const client = new DoubaoRealtimeClient(doubaoConfig);
      doubaoClientRef.current = client;

      // 监听客户端事件
      client.on('connectionStateChange', (state: DoubaoConnectionState) => {
        setConnectionState(state);
        if (state === 'connected') {
          addMessage('system', '已连接到豆包API');
        } else if (state === 'disconnected') {
          addMessage('system', '连接已断开');
          setSessionState('idle');
        } else if (state === 'error') {
          addMessage('system', '连接错误');
        }
      });

      client.on('serverEvent', (event) => {
        handleServerEvent(event);
      });

      client.on('error', (error) => {
        addMessage('system', `错误: ${error.message}`);
      });

      // 连接到豆包API
      await client.connect();

    } catch (error) {
      setConnectionState('error');
      addMessage('system', '连接失败');
      console.error('Connection error:', error);
    }
  }, [config]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (doubaoClientRef.current) {
      doubaoClientRef.current.disconnect();
      doubaoClientRef.current = null;
    }
    setConnectionState('disconnected');
    setSessionState('idle');
  }, []);

  // 开始会话
  const startSession = useCallback(async () => {
    if (connectionState !== 'connected' || !doubaoClientRef.current) {
      return;
    }

    try {
      setSessionState('starting');

      const sessionId = await doubaoClientRef.current.startSession({
        botName: config.botName,
        strictAudit: config.strictAudit
      });

      setSessionState('active');
      sessionIdRef.current = sessionId;
      addMessage('system', '会话已启动');

    } catch (error) {
      setSessionState('error');
      addMessage('system', '会话启动失败');
      console.error('Session start error:', error);
    }
  }, [connectionState, config]);

  // 结束会话
  const endSession = useCallback(() => {
    if (sessionState !== 'active' || !doubaoClientRef.current) {
      return;
    }

    doubaoClientRef.current.endSession();
    setSessionState('idle');
    sessionIdRef.current = null;
    addMessage('system', '会话已结束');
  }, [sessionState]);

  // 开始录音
  const startRecording = useCallback(() => {
    if (!audioRecorderRef.current || sessionState !== 'active') {
      return;
    }

    try {
      setIsRecording(true);
      
      audioRecorderRef.current.startRecording((audioData) => {
        // 发送音频数据到豆包API
        if (doubaoClientRef.current) {
          doubaoClientRef.current.sendAudio(audioData);
        }
      });

      addMessage('user', '开始录音...');
    } catch (error) {
      setIsRecording(false);
      addMessage('system', '录音启动失败');
      console.error('Recording start error:', error);
    }
  }, [sessionState]);

  // 停止录音
  const stopRecording = useCallback(() => {
    if (!audioRecorderRef.current) {
      return;
    }

    audioRecorderRef.current.stopRecording();
    setIsRecording(false);
    addMessage('user', '录音结束');
  }, []);

  // 发送打招呼
  const sayHello = useCallback(() => {
    if (sessionState !== 'active' || !doubaoClientRef.current) {
      return;
    }

    doubaoClientRef.current.sayHello('你好');
    addMessage('user', '你好');
  }, [sessionState]);

  // 处理服务器事件
  const handleServerEvent = useCallback(async (event: any) => {
    switch (event.eventId) {
      case 451: // ASR_RESPONSE
        if (event.data?.results?.[0]?.text) {
          addMessage('user', event.data.results[0].text);
        }
        break;

      case 550: // CHAT_RESPONSE
        if (event.data?.content) {
          addMessage('assistant', event.data.content);
        }
        break;

      case 352: // TTS_RESPONSE
        if (event.audioData && audioPlayerRef.current) {
          try {
            setIsPlaying(true);
            const format = detectAudioFormat(event.audioData);

            if (format === 'ogg') {
              await audioPlayerRef.current.playOggOpus(event.audioData);
            } else {
              await audioPlayerRef.current.playPCM(event.audioData);
            }

            setIsPlaying(false);
          } catch (error) {
            setIsPlaying(false);
            console.error('Audio playback error:', error);
          }
        }
        break;

      default:
        console.log('Unhandled server event:', event.eventId);
    }
  }, []);

  // 添加消息
  const addMessage = useCallback((type: 'user' | 'assistant' | 'system', content: string) => {
    const message: ChatMessage = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // 添加消息
  const addMessage = useCallback((type: 'user' | 'assistant' | 'system', content: string) => {
    const message: ChatMessage = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  }, []);

  // 渲染配置面板
  const renderConfigPanel = () => (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          配置
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">App ID</label>
          <input
            type="text"
            value={config.appId}
            onChange={(e) => setConfig(prev => ({ ...prev, appId: e.target.value }))}
            className="w-full p-2 border rounded"
            placeholder="请输入豆包API的App ID"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">Access Key</label>
          <input
            type="password"
            value={config.accessKey}
            onChange={(e) => setConfig(prev => ({ ...prev, accessKey: e.target.value }))}
            className="w-full p-2 border rounded"
            placeholder="请输入豆包API的Access Key"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">机器人名称</label>
          <input
            type="text"
            value={config.botName}
            onChange={(e) => setConfig(prev => ({ ...prev, botName: e.target.value }))}
            className="w-full p-2 border rounded"
            placeholder="豆包"
          />
        </div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="strictAudit"
            checked={config.strictAudit}
            onChange={(e) => setConfig(prev => ({ ...prev, strictAudit: e.target.checked }))}
          />
          <label htmlFor="strictAudit" className="text-sm">严格审核模式</label>
        </div>
        <Button 
          onClick={() => setShowConfig(false)}
          className="w-full"
          disabled={!config.appId || !config.accessKey}
        >
          确认配置
        </Button>
      </CardContent>
    </Card>
  );

  if (showConfig) {
    return renderConfigPanel();
  }

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-4">
      {/* 状态栏 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge variant={connectionState === 'connected' ? 'default' : 'secondary'}>
                连接: {connectionState}
              </Badge>
              <Badge variant={sessionState === 'active' ? 'default' : 'secondary'}>
                会话: {sessionState}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowConfig(true)}
              >
                <Settings className="w-4 h-4" />
              </Button>
              {connectionState === 'disconnected' ? (
                <Button onClick={connect}>连接</Button>
              ) : (
                <Button variant="outline" onClick={disconnect}>断开</Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 控制面板 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-center gap-4">
            {sessionState === 'idle' && connectionState === 'connected' && (
              <Button onClick={startSession}>开始会话</Button>
            )}
            
            {sessionState === 'active' && (
              <>
                <Button
                  variant={isRecording ? 'destructive' : 'default'}
                  size="lg"
                  onClick={isRecording ? stopRecording : startRecording}
                  className="rounded-full w-16 h-16"
                >
                  {isRecording ? <MicOff className="w-6 h-6" /> : <Mic className="w-6 h-6" />}
                </Button>
                
                <Button variant="outline" onClick={sayHello}>
                  打招呼
                </Button>
                
                <Button variant="outline" onClick={endSession}>
                  结束会话
                </Button>
              </>
            )}
          </div>
          
          {isRecording && (
            <div className="mt-4">
              <Progress value={volume} className="w-full" />
              <p className="text-center text-sm text-gray-500 mt-1">正在录音...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 消息列表 */}
      <Card>
        <CardHeader>
          <CardTitle>对话记录</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`p-3 rounded-lg ${
                  message.type === 'user' 
                    ? 'bg-blue-100 ml-8' 
                    : message.type === 'assistant'
                    ? 'bg-gray-100 mr-8'
                    : 'bg-yellow-50 text-center'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {message.type === 'user' ? '用户' : 
                     message.type === 'assistant' ? '豆包' : '系统'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                <p className="mt-1">{message.content}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
