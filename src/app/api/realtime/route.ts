/**
 * 豆包实时语音API代理路由
 * 由于Next.js API路由不直接支持WebSocket，这里提供HTTP代理功能
 */

import { NextRequest } from 'next/server';

/**
 * 处理GET请求 - 返回WebSocket连接信息
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const appId = url.searchParams.get('appId');
    const accessKey = url.searchParams.get('accessKey');

    if (!appId || !accessKey) {
      return Response.json(
        { error: 'Missing required parameters: appId, accessKey' },
        { status: 400 }
      );
    }

    // 返回WebSocket连接信息
    return Response.json({
      wsUrl: 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue',
      headers: {
        'X-Api-App-ID': appId,
        'X-Api-Access-Key': accessKey,
        'X-Api-Resource-Id': 'volc.speech.dialog'
      },
      config: {
        resourceId: 'volc.speech.dialog',
        supportedModes: ['realtime', 'traditional'],
        audioFormats: ['pcm', 'ogg'],
        sampleRates: [16000, 24000]
      }
    });

  } catch (error) {
    console.error('Error in GET route:', error);
    return Response.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * 处理POST请求（用于非WebSocket的API调用）
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 这里可以处理一些非实时的API调用
    // 比如获取配置、验证凭据等
    
    switch (body.action) {
      case 'validateCredentials':
        // 验证豆包API凭据
        const { appId, accessKey } = body;
        
        if (!appId || !accessKey) {
          return Response.json(
            { error: 'Missing appId or accessKey' },
            { status: 400 }
          );
        }

        // 这里可以添加实际的凭据验证逻辑
        // 目前只是简单检查格式
        const isValid = appId.length > 0 && accessKey.length > 0;
        
        return Response.json({ valid: isValid });

      case 'getConfig':
        // 返回客户端配置
        return Response.json({
          wsUrl: '/api/realtime',
          supportedFormats: ['pcm', 'ogg'],
          sampleRates: [16000, 24000]
        });

      default:
        return Response.json(
          { error: 'Unknown action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in POST route:', error);
    return Response.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * 处理OPTIONS请求（CORS预检）
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}
