/**
 * WebSocket代理路由 - 处理豆包API的WebSocket连接
 * 由于浏览器WebSocket不支持自定义headers，需要通过服务端代理
 */

import { NextRequest } from 'next/server';
import WebSocket from 'ws';

// 豆包API配置
const DOUBAO_CONFIG = {
  appId: process.env.DOUBAO_APP_ID || '2919022726',
  accessToken: process.env.DOUBAO_ACCESS_TOKEN || 'EDQbT-FiT72TPSeOMnePDef1OFKYCAp5',
  resourceId: process.env.DOUBAO_RESOURCE_ID || 'volc.speech.dialog'
};

// WebSocket升级处理
export async function GET(request: NextRequest) {
  try {
    // 检查是否为WebSocket升级请求
    const upgrade = request.headers.get('upgrade');
    if (upgrade !== 'websocket') {
      return new Response('Expected WebSocket upgrade', { status: 400 });
    }

    // 创建到豆包API的WebSocket连接
    const doubaoWsUrl = 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue';
    
    const doubaoWs = new WebSocket(doubaoWsUrl, {
      headers: {
        'X-Api-App-ID': DOUBAO_CONFIG.appId,
        'X-Api-Access-Key': DOUBAO_CONFIG.accessToken,
        'X-Api-Resource-Id': DOUBAO_CONFIG.resourceId,
        'User-Agent': 'DoubaoRealtimeClient/1.0'
      }
    });

    // 注意：在实际的Next.js环境中，WebSocket升级需要特殊处理
    // 这里提供一个基础的实现框架，实际部署时可能需要使用专门的WebSocket服务器
    
    return new Response('WebSocket proxy endpoint', {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
      }
    });

  } catch (error) {
    console.error('WebSocket代理错误:', error);
    return new Response('WebSocket proxy error', { status: 500 });
  }
}

// 由于Next.js的限制，真实的WebSocket代理需要在部署时使用专门的WebSocket服务器
// 这里提供一个说明性的实现

export async function POST(request: NextRequest) {
  return new Response(JSON.stringify({
    message: 'WebSocket代理端点',
    note: '由于Next.js的限制，真实的WebSocket连接需要在生产环境中使用专门的WebSocket服务器',
    config: {
      wsUrl: 'wss://openspeech.bytedance.com/api/v3/realtime/dialogue',
      proxyRequired: true
    }
  }), {
    headers: {
      'Content-Type': 'application/json',
    }
  });
}
