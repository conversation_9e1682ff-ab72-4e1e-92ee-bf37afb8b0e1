/**
 * 演示：为什么浏览器不能直接连接豆包API
 */

import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  return new Response(JSON.stringify({
    title: "为什么需要WebSocket代理？",
    problem: {
      description: "浏览器WebSocket不支持自定义headers",
      example: `
// ❌ 这在浏览器中不工作
const ws = new WebSocket('wss://openspeech.bytedance.com/api/v3/realtime/dialogue', {
  headers: {
    'X-Api-App-ID': 'your-app-id',
    'X-Api-Access-Key': 'your-token'  // 豆包API需要这些认证头
  }
});
      `,
      browserLimitation: "浏览器WebSocket构造函数只接受URL和protocols参数，不支持headers"
    },
    doubaoRequirements: {
      requiredHeaders: [
        "X-Api-App-ID",
        "X-Api-Access-Key",
        "X-Api-Resource-Id"
      ],
      authenticationMethod: "通过HTTP headers进行认证"
    },
    solutions: {
      option1: {
        name: "WebSocket代理服务器",
        description: "在服务端创建代理，转发客户端消息到豆包API",
        implementation: "使用自定义Next.js服务器 + ws库"
      },
      option2: {
        name: "HTTP轮询",
        description: "使用HTTP API替代WebSocket（不推荐，延迟高）"
      },
      option3: {
        name: "服务端渲染",
        description: "完全在服务端处理，通过SSE推送结果"
      }
    },
    currentImplementation: "我们使用方案1：WebSocket代理服务器"
  }), {
    headers: {
      'Content-Type': 'application/json',
    }
  });
}

export async function POST(request: NextRequest) {
  const body = await request.json();

  if (body.action === 'testDirectConnection') {
    return new Response(JSON.stringify({
      success: false,
      error: "浏览器WebSocket不支持自定义headers",
      explanation: "豆包API需要认证headers，但浏览器WebSocket API不支持设置headers",
      workaround: "必须使用服务端代理"
    }), {
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }

  return new Response(JSON.stringify({
    message: "WebSocket代理说明端点"
  }), {
    headers: {
      'Content-Type': 'application/json',
    }
  });
}
