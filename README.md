# 豆包实时语音对话应用

基于豆包端到端实时语音大模型API构建的语音交互应用，支持实时语音对话功能。

## 功能特性

- 🎤 **实时语音录制** - 支持浏览器麦克风录音，PCM格式音频采集
- 🔊 **音频播放** - 支持OGG/Opus和PCM格式音频播放
- 💬 **语音对话** - 集成豆包实时语音大模型API，支持语音到语音的对话
- 🎨 **现代UI** - 使用shadcn/ui组件库，响应式设计
- ⚡ **实时交互** - WebSocket连接，低延迟语音交互
- 🛡️ **安全审核** - 支持严格审核模式配置

## 技术栈

- **前端框架**: Next.js 15 + React 19
- **UI组件**: shadcn/ui + Tailwind CSS
- **音频处理**: Web Audio API
- **实时通信**: WebSocket
- **语言**: TypeScript

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 3. 配置API密钥

在应用界面中输入你的豆包API配置：
- **App ID**: 从火山引擎控制台获取
- **Access Key**: 从火山引擎控制台获取

## 使用说明

### 基本流程

1. **配置API密钥** - 在配置面板中输入豆包API的App ID和Access Key
2. **连接服务** - 点击"连接"按钮建立与豆包API的连接
3. **开始会话** - 连接成功后点击"开始会话"
4. **语音交互** - 点击麦克风按钮开始录音，松开结束录音
5. **查看对话** - 在对话记录中查看语音识别结果和AI回复

### 功能按钮

- 🎤 **录音按钮** - 按住录音，松开发送
- 👋 **打招呼** - 发送文本问候
- ⚙️ **设置** - 修改API配置
- 🔌 **连接/断开** - 管理API连接状态

## API文档参考

本项目基于豆包端到端实时语音大模型API开发，详细API文档请参考：
[豆包实时语音API文档](https://www.volcengine.com/docs/6561/1594356)

## 注意事项

### 演示模式

当前版本运行在演示模式下，模拟了豆包API的响应。在生产环境中需要：

1. **服务端代理** - 由于浏览器WebSocket不支持自定义headers，需要通过服务端代理处理认证
2. **真实API集成** - 替换演示模式的模拟响应为真实API调用
3. **错误处理** - 完善生产环境的错误处理和重试机制

### 浏览器兼容性

- 需要支持Web Audio API的现代浏览器
- 需要麦克风权限
- 建议使用Chrome、Firefox、Safari等主流浏览器

## 许可证

MIT License
